import { PrismaClient } from '@prisma/client';
import * as fs from 'fs';
import * as path from 'path';

const prisma = new PrismaClient();

// Load JSON data files
const loadJsonData = (filename: string) => {
  const filePath = path.join(__dirname, '..', 'data', filename);
  const rawData = fs.readFileSync(filePath, 'utf8');
  return JSON.parse(rawData);
};

async function seedLocationData() {
  console.log('🌍 Starting comprehensive location data seeding...');

  try {
    // Step 1: Create system user first
    console.log('👤 Creating system user...');

    // Check if system user already exists
    let systemUser = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    });

    if (!systemUser) {
      systemUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          firstName: 'System',
          lastName: 'User',
          type: 'system',
          phoneNumber: '+1000000000',
          countryDialCode: '+1',
          status: 'active'
        }
      });
      console.log(`✅ Created system user with ID: ${systemUser.userId}`);
    } else {
      console.log(`✅ System user already exists with ID: ${systemUser.userId}`);
    }

    const systemUserId = systemUser.userId;

    // Step 2: Clear existing location data (optional)
    console.log('🗑️  Clearing existing location data...');
    await prisma.city.deleteMany();
    await prisma.state.deleteMany();
    await prisma.country.deleteMany();

    // Step 3: Load data from JSON files
    console.log('📂 Loading data from JSON files...');
    const countriesData = loadJsonData('countries.json');
    const statesData = loadJsonData('states.json');
    const citiesData = loadJsonData('cities.json');

    console.log(`📊 Loaded data:
    - Countries: ${countriesData.length}
    - States: ${statesData.length}
    - Cities: ${citiesData.length}`);

    // Step 4: Seed countries
    console.log('🏳️  Seeding countries...');
    const createdCountries = [];

    for (const country of countriesData) {
      try {
        const createdCountry = await prisma.country.create({
          data: {
            isoCode2: country.isoCode2,
            isoCode3: country.isoCode3,
            numericCode: country.numericCode,
            name: country.name,
            phonecode: country.phonecode,
            flag: country.flag,
            currency: country.currency,
            currencySymbol: country.currencySymbol,
            currencyName: country.currencyName,
            latitude: country.latitude ? parseFloat(country.latitude.toString()) : null,
            longitude: country.longitude ? parseFloat(country.longitude.toString()) : null,
            timezones: country.timezones || [],
            status: country.status || 'active',
            createdBy: systemUserId,
            updatedBy: systemUserId
          }
        });
        createdCountries.push(createdCountry);
      } catch (error) {
        console.warn(`⚠️  Skipped country ${country.name}: ${error.message}`);
      }
    }
    console.log(`✅ Created ${createdCountries.length} countries`);

    // Create country lookup map using ISO codes
    const countryMap = new Map();
    createdCountries.forEach(country => {
      countryMap.set(country.isoCode2, country.countryId);
    });

    // Step 5: Seed states
    console.log('🏛️  Seeding states...');
    const createdStates = [];

    for (const state of statesData) {
      try {
        const countryId = countryMap.get(state.countryCode);
        if (!countryId) {
          console.warn(`⚠️  Country not found for state ${state.name} with country code: ${state.countryCode}`);
          continue;
        }

        const createdState = await prisma.state.create({
          data: {
            name: state.name,
            stateCode: state.stateCode || `${state.name.substring(0, 3).toUpperCase()}`,
            countryId: countryId,
            latitude: state.latitude ? parseFloat(state.latitude.toString()) : null,
            longitude: state.longitude ? parseFloat(state.longitude.toString()) : null,
            status: state.status || 'active',
            createdBy: systemUserId,
            updatedBy: systemUserId
          }
        });
        createdStates.push(createdState);
      } catch (error) {
        console.warn(`⚠️  Skipped state ${state.name}: ${error.message}`);
      }
    }
    console.log(`✅ Created ${createdStates.length} states`);

    // Create state lookup map using state codes
    const stateMap = new Map();
    createdStates.forEach(state => {
      stateMap.set(state.stateCode, state.stateId);
    });

    // Step 6: Seed cities
    console.log('🏙️  Seeding cities...');
    const createdCities = [];

    for (const city of citiesData) {
      try {
        const stateId = stateMap.get(city.stateCode);
        if (!stateId) {
          console.warn(`⚠️  State not found for city ${city.name} with state code: ${city.stateCode}`);
          continue;
        }

        const createdCity = await prisma.city.create({
          data: {
            name: city.name,
            stateId: stateId,
            latitude: city.latitude ? parseFloat(city.latitude.toString()) : null,
            longitude: city.longitude ? parseFloat(city.longitude.toString()) : null,
            status: city.status || 'active',
            createdBy: systemUserId,
            updatedBy: systemUserId
          }
        });
        createdCities.push(createdCity);
      } catch (error) {
        console.warn(`⚠️  Skipped city ${city.name}: ${error.message}`);
      }
    }
    console.log(`✅ Created ${createdCities.length} cities`);

    console.log('🎉 Location data seeding completed successfully!');
    console.log(`📊 Final Summary:
    - System User: ${systemUser.email} (ID: ${systemUserId})
    - Countries: ${createdCountries.length}
    - States: ${createdStates.length}
    - Cities: ${createdCities.length}

    All records created with system user as creator and updater.`);

  } catch (error) {
    console.error('❌ Error seeding location data:', error);
    throw error;
  } finally {
    await prisma.$disconnect();
  }
}

// Run the seeding function
if (require.main === module) {
  seedLocationData()
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}

export default seedLocationData;
