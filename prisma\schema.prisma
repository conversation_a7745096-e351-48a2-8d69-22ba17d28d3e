// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model User {
  userId            Int       @id @default(autoincrement()) @map("user_id")
  email             String    @unique @db.VarChar(255)
  firstName         String?   @map("first_name") @db.VarChar(100)
  lastName          String?   @map("last_name") @db.VarChar(100)
  profileImage      String?   @map("profile_image") @db.VarChar(255)
  type              UserType
  phoneNumber       String    @map("phone_number") @db.VarChar(20)
  countryDialCode   String    @map("country_dial_code") @db.VarChar(5)
  candidateId       Int?      @map("candidate_id")
  recruiterId       Int?      @map("recruiter_id")
  createdAt         DateTime  @default(now()) @map("created_at")
  updatedAt         DateTime  @updatedAt @map("updated_at")
  createdBy         Int?      @map("created_by")
  updatedBy         Int?      @map("updated_by")
  status            String    @db.VarChar(50)

  // Relations
  candidate         Candidate? @relation(fields: [candidateId], references: [candidateId])
  recruiter         Recruiter? @relation(fields: [recruiterId], references: [recruiterId])
  userSessions      UserSession[]

  @@map("Users")
}

model Candidate {
  candidateId                        Int       @id @default(autoincrement()) @map("candidate_id")
  resume                            Int?
  currentLocationId                 Int?      @map("current_location_id")
  summary                           String?   @db.VarChar(50)
  totalExpirenceMonths              Int?      @map("total_expirence_months")
  totalExpirenceWithoutInternMonths Int?      @map("total_expirence_without_intern_months")
  createdAt                         DateTime  @default(now()) @map("created_at")
  updatedAt                         DateTime  @updatedAt @map("updated_at")
  createdBy                         Int?      @map("created_by")
  updatedBy                         Int?      @map("updated_by")

  // Relations
  users             User[]
  currentLocation   Location? @relation(fields: [currentLocationId], references: [locationId])

  @@map("Candidates")
}

model Location {
  locationId     Int       @id @default(autoincrement()) @map("location_id")
  addressLine1   String    @map("address_line_1") @db.VarChar(255)
  addressLine2   String?   @map("address_line_2") @db.VarChar(255)
  cityId         Int       @map("city_id")
  stateId        Int       @map("state_id")
  countryId      Int       @map("country_id")
  zipCode        String    @map("zip_code") @db.VarChar(20)
  latitude       Decimal?  @db.Decimal(10, 8)
  longitude      Decimal?  @db.Decimal(11, 8)
  createdAt      DateTime  @default(now()) @map("created_at")
  createdBy      Int?      @map("created_by")
  updatedAt      DateTime  @updatedAt @map("updated_at")
  updatedBy      Int?      @map("updated_by")

  // Relations
  city           City      @relation(fields: [cityId], references: [cityId])
  state          State     @relation(fields: [stateId], references: [stateId])
  country        Country   @relation(fields: [countryId], references: [countryId])
  candidates     Candidate[]
  organizationLocations OrganizationLocation[]

  @@map("Locations")
}

model OrganizationLocation {
  organizationLocationId Int          @id @default(autoincrement()) @map("organization_location_id")
  locationId             Int          @map("location_id")
  organizationId         Int          @map("organization_id")
  isDefault              Boolean      @default(false)
  createdAt              DateTime     @default(now()) @map("created_at")
  createdBy              Int?         @map("created_by")
  updatedAt              DateTime     @updatedAt @map("updated_at")
  updatedBy              Int?         @map("updated_by")

  // Relations
  location               Location     @relation(fields: [locationId], references: [locationId])
  organization           Organization @relation(fields: [organizationId], references: [organizationId])

  @@map("Organization_Locations")
}

model Organization {
  organizationId       Int                    @id @default(autoincrement()) @map("organization_id")
  name                 String                 @db.VarChar(255)
  domain               String?                @db.VarChar(255)
  email                String?                @db.VarChar(255)
  phoneNumber          String?                @map("phone_number") @db.VarChar(20)
  logo                 String?                @db.VarChar(255)
  organizationOverview String?                @map("organization_overview") @db.Text
  createdAt            DateTime               @default(now()) @map("created_at")
  updatedAt            DateTime               @updatedAt @map("updated_at")
  createdBy            Int?                   @map("created_by")
  updatedBy            Int?                   @map("updated_by")
  status               String                 @db.VarChar(50)

  // Relations
  recruiters           Recruiter[]
  roles                Role[]
  organizationLocations OrganizationLocation[]

  @@map("Organizations")
}

model Recruiter {
  recruiterId    Int          @id @default(autoincrement()) @map("recruiter_id")
  organizationId Int          @map("organization_id")
  roleId         Int          @map("role_id")
  status         String       @db.VarChar(50)
  createdAt      DateTime     @default(now()) @map("created_at")
  updatedAt      DateTime     @updatedAt @map("updated_at")
  createdBy      Int?         @map("created_by")
  updatedBy      Int?         @map("updated_by")

  // Relations
  organization   Organization @relation(fields: [organizationId], references: [organizationId])
  role           Role         @relation(fields: [roleId], references: [roleId])
  users          User[]

  @@map("Recruiters")
}

model Role {
  roleId         Int              @id @default(autoincrement()) @map("role_id")
  name           String           @db.VarChar(100)
  organizationId Int?             @map("organization_id")
  global         Boolean          @default(false)
  createdAt      DateTime         @default(now()) @map("created_at")
  createdBy      Int?             @map("created_by")
  updatedAt      DateTime         @updatedAt @map("updated_at")
  updatedBy      Int?             @map("updated_by")
  status         Int

  // Relations
  organization   Organization?    @relation(fields: [organizationId], references: [organizationId])
  recruiters     Recruiter[]
  rolePermissions RolePermission[]

  @@map("Roles")
}

model Permission {
  permissionId Int              @id @default(autoincrement()) @map("permission_id")
  description  String           @db.VarChar(255)
  module       String           @db.VarChar(100)
  code         String           @db.VarChar(8)
  createdAt    DateTime         @default(now()) @map("created_at")
  createdBy    Int?             @map("created_by")
  updatedAt    DateTime         @updatedAt @map("updated_at")
  updatedBy    Int?             @map("updated_by")
  status       String           @db.VarChar(50)

  // Relations
  rolePermissions RolePermission[]

  @@map("Permissions")
}

model RolePermission {
  rolePermissionId Int        @id @default(autoincrement()) @map("role_permission_id")
  roleId           Int        @map("role_id")
  permissionId     Int        @map("permission_id")
  createdAt        DateTime   @default(now()) @map("created_at")
  createdBy        Int?       @map("created_by")
  updatedAt        DateTime   @updatedAt @map("updated_at")
  updatedBy        Int?       @map("updated_by")

  // Relations
  role             Role       @relation(fields: [roleId], references: [roleId])
  permission       Permission @relation(fields: [permissionId], references: [permissionId])

  @@map("Role_Permissions")
}

model UserSession {
  userSessionsId Int           @id @default(autoincrement()) @map("user_sessions_id")
  userId         Int           @map("user_id")
  ipAddress      String        @map("ip_address") @db.VarChar(45)
  userAgent      String        @map("user_agent") @db.VarChar(255)
  loginMethod    LoginMethod   @map("login_method")
  issuedAt       DateTime      @map("issued_at")
  expiresAt      DateTime      @map("expires_at")
  status         String        @db.VarChar(50)

  // Relations
  user           User          @relation(fields: [userId], references: [userId])

  @@map("User_Sessions")
}

model City {
  cityId     Int       @id @default(autoincrement()) @map("city_id")
  name       String    @map("name") @db.VarChar(100)
  stateId    Int       @map("state_id")
  latitude   Decimal?  @map("latitude") @db.Decimal(10, 8)
  longitude  Decimal?  @map("longitude") @db.Decimal(11, 8)
  createdAt  DateTime  @default(now()) @map("created_at")
  createdBy  Int?      @map("created_by")
  updatedAt  DateTime  @updatedAt @map("updated_at")
  updatedBy  Int?      @map("updated_by")

  status     Status    @default(active)

  // Relations
  state      State     @relation(fields: [stateId], references: [stateId])
  locations  Location[]

  @@map("cities")
}

model State {
  stateId    Int        @id @default(autoincrement()) @map("state_id")
  name       String     @map("name") @db.VarChar(100)
  stateCode    String     @map("state_code") @db.VarChar(10) // State/Province code
  countryId  Int        @map("country_id")
  latitude   Decimal?   @map("latitude") @db.Decimal(10, 8)
  longitude  Decimal?   @map("longitude") @db.Decimal(11, 8)
  createdAt  DateTime   @default(now()) @map("created_at")
  createdBy  Int?       @map("created_by")
  updatedAt  DateTime   @updatedAt @map("updated_at")
  updatedBy  Int?       @map("updated_by")
  status     Status     @default(active)

  // Relations
  country    Country    @relation(fields: [countryId], references: [countryId])
  cities     City[]
  locations  Location[]

  @@map("states")
}

model Country {
  countryId    Int       @id @default(autoincrement()) @map("country_id")
  isoCode2      String    @unique @map("iso_code") @db.VarChar(2) // ISO 3166-1 alpha-2
  isoCode3     String    @unique @map("iso_code_3") @db.VarChar(3) // ISO 3166-1 alpha-3
  numericCode  String?   @unique @map("numeric_code") @db.VarChar(3) // ISO 3166-1 numeric
  name         String    @map("name") @db.VarChar(100)
  phonecode    String    @map("phonecode") @db.VarChar(10) // Phone code without +
  flag         String?   @map("flag") @db.VarChar(10) // Country flag emoji
  currency     String?   @map("currency") @db.VarChar(10) // Currency code like USD, INR
  currencySymbol String? @map("currency_symbol") @db.VarChar(10) // Currency symbol like $, ₹
  currencyName String? @map("currency_name") @db.VarChar(100) // Currency name like US Dollar, Indian Rupee
  latitude     Decimal?  @map("latitude") @db.Decimal(10, 8)
  longitude    Decimal?  @map("longitude") @db.Decimal(11, 8)
  timezones    Json?     @map("timezones") // JSON array of timezone objects
  createdAt    DateTime  @default(now()) @map("created_at")
  createdBy    Int?      @map("created_by")
  updatedAt    DateTime  @updatedAt @map("updated_at")
  updatedBy    Int?      @map("updated_by")
  status       Status    @default(active)


  // Relations
  states       State[]
  locations    Location[]

  @@map("countries")
}

model Otp {
  otpId     Int      @id @default(autoincrement()) @map("otp_id")
  otp       Int
  tokenId   Int      @map("token_id")
  expiredAt DateTime @map("expired_at")
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("otp")
}

enum UserType {
  candidate
  recruiter
  system
}

enum LoginMethod {
  google
  microsoft
  linkedin
  otp
}

enum Status {
  active
  inactive
}
